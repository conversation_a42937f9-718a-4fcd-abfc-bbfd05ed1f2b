CREATE CATALOG paimon_catalog WITH (
  -- 'type' = 'table-store',
  'type' = 'paimon',
  'warehouse' = 'hdfs:///user/hh_slfn2_sschj/paimon',
  'default-database' = 'ubd_sscj_prod_flink'
);
USE CATALOG paimon_catalog;
CREATE CATALOG hive_catalog WITH (
  'type' = 'hive',
  'default-database' = 'ubd_sscj_prod_flink',
  'hive-conf-dir' = 'hdfs:///user/hh_slfn2_sschj/hive/conf'
);
USE CATALOG hive_catalog;
SET
  'sql-client.execution.result-mode' = 'tableau';
SET
  'yarn.application.queue' = 'hh_slfn2_sschj';
SET
  'table.exec.sink.not-null-enforcer' = 'DROP';
SET
  'table.exec.sink.upsert-materialize' = 'NONE';

-- 新的Kafka表
CREATE TABLE IF NOT EXISTS `hive_catalog`.`ubd_sscj_prod_flink`.`ods_r_kafka_huafeiquan_new` (
  RAW_FIELD STRING,
  DATA_RECEIVE_TIME TIMESTAMP(3) METADATA FROM 'timestamp'
) WITH (
  'connector' = 'kafka',
  'properties.bootstrap.servers' = '10.169.10.239:9092,10.169.10.240:9092,10.169.10.241:9092,10.169.10.242:9092,10.169.10.243:9092',
  'properties.security.protocol' = 'SASL_PLAINTEXT',
  'properties.sasl.mechanism' = 'SCRAM-SHA-256',
  'properties.group.id' = 'dataMiddlePlatform',
  'properties.sasl.jaas.config' = 'org.apache.kafka.common.security.scram.ScramLoginModule required username="dataMiddlePlatform" password="Clnt-Sm^34o?Jt";',
  'topic' = 'prizeInfoTopic',
  'scan.topic-partition-discovery.interval' = '30000',
  'properties.session.timeout.ms' = '300000',
  'format' = 'raw'
);

CREATE TABLE IF NOT EXISTS `paimon_catalog`.`ubd_sscj_prod_flink`.`dwa_r_paimon_huafeiquan_new` (
    `serial_number` STRING,
    `user_id` STRING,
    `province_code` STRING,
    `ACTIVATE_FLAG` BOOLEAN,
    CONSTRAINT `pk` PRIMARY KEY (`serial_number`) NOT ENFORCED
) WITH (
  'bucket' = '128',
  'bucket-key' = 'serial_number',
  'file.format' = 'avro',
  'merge-engine' = 'partial-update',
  'changelog-producer' = 'lookup',
  'write-buffer-size' = '256MB',
  'write-only' = 'true'
);





-- 入湖
INSERT INTO `paimon_catalog`.`ubd_sscj_prod_flink`.`dwa_r_paimon_huafeiquan_new`(serial_number,ACTIVATE_FLAG)
SELECT
  JSON_VALUE(RAW_FIELD, '$.mobile') AS serial_number,
  true AS ACTIVATE_FLAG
FROM
  `hive_catalog`.`ubd_sscj_prod_flink`.`ods_r_kafka_huafeiquan_new`
  /*+ OPTIONS('properties.group.id'='dataMiddlePlatform','scan.startup.mode'='earliest-offset','properties.enable.auto.commit'='true','properties.auto.offset.reset.strategy'='latest','properties.auto.commit.interval.ms'='1000')*/
;