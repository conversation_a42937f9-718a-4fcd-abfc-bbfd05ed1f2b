-- 大文件优化版本：分批处理10G+文件到Paimon表
-- 适用于超大文件的分批加载策略

-- 1. 设置Paimon Catalog
CREATE CATALOG paimon_catalog WITH (
  'type' = 'paimon',
  'warehouse' = 'hdfs:///user/hh_slfn2_sschj/paimon',
  'default-database' = 'ubd_sscj_prod_flink'
);
USE CATALOG paimon_catalog;

-- 2. 大文件处理的优化参数
SET 'sql-client.execution.result-mode' = 'tableau';
SET 'table.exec.sink.upsert-materialize' = 'NONE';
SET 'table.exec.sink.not-null-enforcer' = 'DROP';
-- 增加并行度和内存配置
SET 'table.exec.resource.default-parallelism' = '16';
SET 'pipeline.max-parallelism' = '512';
SET 'taskmanager.memory.process.size' = '8g';
SET 'taskmanager.memory.flink.size' = '6g';
-- 检查点配置
SET 'execution.checkpointing.interval' = '5min';
SET 'execution.checkpointing.mode' = 'EXACTLY_ONCE';
-- 重启策略
SET 'restart-strategy' = 'fixed-delay';
SET 'restart-strategy.fixed-delay.attempts' = '3';
SET 'restart-strategy.fixed-delay.delay' = '30s';

-- 3. 方案一：创建临时表读取文件（在Paimon Catalog下必须使用TEMPORARY）
CREATE TEMPORARY TABLE large_file_source (
  raw_line STRING  -- 先读取整行数据
) WITH (
  'connector' = 'filesystem',
  'path' = 'hdfs://slfn2/warehouse/tablespace/external/hive/ubd_sscj_prod_flink.db/lcz/*.txt',
  'format' = 'raw',
  'raw.charset' = 'UTF-8',
  'raw.endline' = '\u0002'  -- 行分隔符
);

-- 4. 创建解析后的临时视图
CREATE TEMPORARY VIEW parsed_data AS
SELECT
  SPLIT_INDEX(raw_line, '\u0001', 0) AS PARTITION_ID,
  SPLIT_INDEX(raw_line, '\u0001', 1) AS USER_ID,
  SPLIT_INDEX(raw_line, '\u0001', 2) AS SERVICE_ID,
  SPLIT_INDEX(raw_line, '\u0001', 3) AS MAIN_TAG,
  SPLIT_INDEX(raw_line, '\u0001', 4) AS START_DATE,
  SPLIT_INDEX(raw_line, '\u0001', 5) AS END_DATE,
  SPLIT_INDEX(raw_line, '\u0001', 6) AS SERVICE_ITEM_ID,
  SPLIT_INDEX(raw_line, '\u0001', 7) AS PACKAGE_ID,
  SPLIT_INDEX(raw_line, '\u0001', 8) AS PRODUCT_ID,
  SPLIT_INDEX(raw_line, '\u0001', 9) AS UPDATE_TIME,
  SPLIT_INDEX(raw_line, '\u0001', 10) AS EPARCHY_CODE,
  SPLIT_INDEX(raw_line, '\u0001', 11) AS PROVINCE_CODE,
  SPLIT_INDEX(raw_line, '\u0001', 12) AS PRIOR_ORDER_TIME,
  SPLIT_INDEX(raw_line, '\u0001', 13) AS opt,
  SPLIT_INDEX(raw_line, '\u0001', 14) AS opttime,
  SPLIT_INDEX(raw_line, '\u0001', 15) AS cdhtime
FROM large_file_source
WHERE raw_line IS NOT NULL;

-- 5. 分批写入Paimon表（使用流式处理）
INSERT INTO `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_tf_f_user_service_temp1`
SELECT
  PARTITION_ID,
  USER_ID,
  SERVICE_ID,
  MAIN_TAG,
  START_DATE,
  END_DATE,
  SERVICE_ITEM_ID,
  PACKAGE_ID,
  PRODUCT_ID,
  UPDATE_TIME,
  EPARCHY_CODE,
  PROVINCE_CODE,
  PRIOR_ORDER_TIME,
  opt,
  opttime,
  cdhtime,
  NULL AS kafka_in_time,
  NULL AS kafka_out_time,
  CAST(CURRENT_TIMESTAMP AS TIMESTAMP(3)) AS paimon_time,
  CAST(NULL AS MAP<STRING,BYTES>) AS headers
FROM parsed_data
WHERE USER_ID IS NOT NULL AND SERVICE_ID IS NOT NULL AND START_DATE IS NOT NULL;
