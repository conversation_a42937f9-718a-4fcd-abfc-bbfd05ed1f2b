#!/bin/bash
# 上传大文件到HDFS并通过FlinkSQL加载到Paimon表

# 1. 上传文件到HDFS
echo "正在上传文件到HDFS..."
hdfs dfs -mkdir -p /user/hh_slfn2_sschj/data/external_call_data/
hdfs dfs -put /path/to/your/large-file.txt /user/hh_slfn2_sschj/data/external_call_data/

# 2. 检查文件是否上传成功
echo "检查文件上传状态..."
hdfs dfs -ls /user/hh_slfn2_sschj/data/external_call_data/
hdfs dfs -du -h /user/hh_slfn2_sschj/data/external_call_data/large-file.txt

# 3. 可选：分割大文件为多个小文件（如果单文件过大）
echo "可选：分割大文件..."
# hdfs dfs -D dfs.blocksize=128m -put /path/to/your/large-file.txt /user/hh_slfn2_sschj/data/external_call_data/

# 4. 执行FlinkSQL加载
echo "开始执行FlinkSQL加载..."
flink sql-client embedded \
  --jar /path/to/flink-connector-filesystem.jar \
  --jar /path/to/paimon-flink.jar \
  -f load_large_txt_to_paimon_optimized.sql

echo "数据加载完成！"
