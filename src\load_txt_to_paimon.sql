-- 加载大型TXT文件到Paimon表的FlinkSQL方案
-- 文件分隔符：行分隔符 \002，列分隔符 \001

-- 1. 设置Paimon Catalog
CREATE CATALOG paimon_catalog WITH (
  'type' = 'paimon',
  'warehouse' = 'hdfs:///user/hh_slfn2_sschj/paimon',
  'default-database' = 'ubd_sscj_prod_flink'
);
USE CATALOG paimon_catalog;

-- 2. 设置执行参数
SET 'sql-client.execution.result-mode' = 'tableau';
SET 'table.exec.sink.upsert-materialize' = 'NONE';
SET 'table.exec.sink.not-null-enforcer' = 'DROP';
-- 针对大文件处理的优化参数
SET 'table.exec.resource.default-parallelism' = '8';
SET 'pipeline.max-parallelism' = '256';
SET 'taskmanager.memory.process.size' = '4g';
SET 'taskmanager.memory.flink.size' = '3g';

-- 3. 创建临时文件源表（读取您的txt文件）
CREATE TEMPORARY TABLE file_source_table (
  PARTITION_ID STRING,
  USER_ID STRING,
  SERVICE_ID STRING,
  MAIN_TAG STRING,
  START_DATE STRING,
  END_DATE STRING,
  SERVICE_ITEM_ID STRING,
  PACKAGE_ID STRING,
  PRODUCT_ID STRING,
  UPDATE_TIME STRING,
  EPARCHY_CODE STRING,
  PROVINCE_CODE STRING,
  PRIOR_ORDER_TIME STRING,
  opt STRING,
  opttime STRING,
  cdhtime STRING
) WITH (
  'connector' = 'filesystem',
  'path' = 'hdfs://your-hdfs-path/your-file.txt',  -- 替换为您的文件路径
  'format' = 'csv',
  'csv.field-delimiter' = '\u0001',  -- 列分隔符 \001
  'csv.line-delimiter' = '\u0002',   -- 行分隔符 \002
  'csv.ignore-parse-errors' = 'true',
  'csv.allow-comments' = 'false',
  'csv.quote-character' = '',
  'csv.escape-character' = '',
  'csv.null-literal' = ''
);

-- 4. 确保目标Paimon表存在（已在您的外呼数据.sql中定义）
-- ods_r_paimon_tf_f_user_service_temp1 表已存在

-- 5. 数据写入到Paimon表
INSERT INTO `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_tf_f_user_service_temp1`
SELECT
  PARTITION_ID,
  USER_ID,
  SERVICE_ID,
  MAIN_TAG,
  START_DATE,
  END_DATE,
  SERVICE_ITEM_ID,
  PACKAGE_ID,
  PRODUCT_ID,
  UPDATE_TIME,
  EPARCHY_CODE,
  PROVINCE_CODE,
  PRIOR_ORDER_TIME,
  opt,
  opttime,
  cdhtime,
  NULL AS kafka_in_time,
  NULL AS kafka_out_time,
  CAST(CURRENT_TIMESTAMP AS TIMESTAMP(3)) AS paimon_time,
  CAST(NULL AS MAP<STRING,BYTES>) AS headers
FROM file_source_table;
