# Flink依赖包问题解决方案

## 错误原因
`ClassNotFoundException: org.apache.flink.table.planner.delegation.DialectFactory` 表示缺少Flink Table Planner相关的JAR包。

## 解决方案

### 方案1：检查并添加必需的JAR包
确保以下JAR包在Flink的lib目录中：

```bash
# 检查Flink lib目录
ls $FLINK_HOME/lib/

# 必需的JAR包：
flink-table-planner_2.12-1.17.x.jar
flink-table-api-java-bridge_2.12-1.17.x.jar
flink-connector-files-1.17.x.jar
paimon-flink-1.17-x.x.x.jar
```

### 方案2：使用SQL Client启动命令添加JAR包
```bash
# 启动SQL Client时指定JAR包
$FLINK_HOME/bin/sql-client.sh embedded \
  --jar $FLINK_HOME/lib/flink-table-planner_2.12-1.17.x.jar \
  --jar $FLINK_HOME/lib/flink-connector-files-1.17.x.jar \
  --jar /path/to/paimon-flink-1.17-x.x.x.jar
```

### 方案3：在SQL中动态添加JAR包
```sql
-- 在执行SQL前添加JAR包
ADD JAR '/path/to/flink-table-planner_2.12-1.17.x.jar';
ADD JAR '/path/to/flink-connector-files-1.17.x.jar';
ADD JAR '/path/to/paimon-flink-1.17-x.x.x.jar';
```

### 方案4：检查Flink配置
检查 `$FLINK_HOME/conf/flink-conf.yaml` 中的配置：

```yaml
# 确保启用Table API
table.exec.legacy-cast-behaviour: ENABLED
table.optimizer.join-reorder-enabled: true
```
