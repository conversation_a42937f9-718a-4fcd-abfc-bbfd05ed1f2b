# 图片题目提取工具

这个工具可以从PNG图片中提取考试题目和答案，并保存到txt文件中。

## 使用步骤

### 1. 安装依赖
首先需要安装Python和所需的库。

**方法一：使用批处理文件（推荐）**
双击运行 `install_requirements.bat` 文件

**方法二：手动安装**
打开命令提示符，运行：
```bash
pip install easyocr
```

### 2. 运行脚本
```bash
python extract_questions.py
```

## 功能说明

- **自动识别**：使用OCR技术自动识别图片中的中文和英文文字
- **智能解析**：自动识别题目、选项（A、B、C、D）和正确答案
- **批量处理**：一次性处理指定文件夹中的所有PNG图片
- **结果保存**：将提取的题目保存到a.txt文件中

## 输出格式

每道题目的格式如下：
```
题目 1:
问题: 以下哪种技术不属于数字化基础技术？
选项:
  A 云计算
  B 区块链
  C 活字印刷术
  D 大数据
正确答案: C
原始文本:
[从图片中提取的完整文字内容]
--------------------------------------------------
```

## 注意事项

1. **图片质量**：确保图片清晰，文字可读
2. **文件格式**：目前只支持PNG格式
3. **文件路径**：默认处理路径为 `C:\Users\<USER>\Desktop\考试题\`
4. **首次运行**：第一次运行时会下载OCR模型，可能需要一些时间

## 自定义设置

如果需要修改文件路径，请编辑 `extract_questions.py` 文件中的以下行：
```python
image_folder = r"C:\Users\<USER>\Desktop\考试题"
```

## 故障排除

1. **找不到图片**：检查文件夹路径是否正确
2. **识别效果不好**：尝试提高图片分辨率或清晰度
3. **安装失败**：确保已安装Python，并且网络连接正常

## 系统要求

- Python 3.6 或更高版本
- 足够的磁盘空间（OCR模型约100MB）
- 网络连接（首次下载模型时需要）
