#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片题目提取脚本
从PNG图片中提取问题和答案，保存到txt文件
"""

import os
import re
from pathlib import Path
try:
    import easyocr
except ImportError:
    print("需要安装easyocr库: pip install easyocr")
    exit(1)

def extract_text_from_image(image_path, reader):
    """
    从图片中提取文字
    """
    try:
        results = reader.readtext(image_path)
        text_lines = []
        for (bbox, text, confidence) in results:
            if confidence > 0.5:  # 只保留置信度较高的文字
                text_lines.append(text.strip())
        return '\n'.join(text_lines)
    except Exception as e:
        print(f"处理图片 {image_path} 时出错: {e}")
        return ""

def parse_question_content(text):
    """
    解析题目内容，提取问题、选项和答案
    """
    lines = text.split('\n')
    question = ""
    options = []
    correct_answer = ""
    
    # 查找题目
    for i, line in enumerate(lines):
        line = line.strip()
        if not line:
            continue
            
        # 检查是否是题目（通常包含问号或者是第一行有意义的文字）
        if '?' in line or '？' in line or (not question and len(line) > 5):
            if not question:
                question = line
        
        # 检查选项 A, B, C, D
        if re.match(r'^[ABCD]\s+', line):
            options.append(line)
        
        # 查找正确答案
        if '正确答案' in line or '答案' in line:
            # 提取答案字母
            answer_match = re.search(r'[ABCD]', line)
            if answer_match:
                correct_answer = answer_match.group()
    
    return question, options, correct_answer

def process_images(image_folder, output_file):
    """
    处理所有图片并提取题目
    """
    # 初始化OCR读取器（支持中文和英文）
    print("正在初始化OCR引擎...")
    reader = easyocr.Reader(['ch_sim', 'en'])
    
    image_folder = Path(image_folder)
    if not image_folder.exists():
        print(f"错误：文件夹 {image_folder} 不存在")
        return
    
    # 获取所有PNG文件
    png_files = list(image_folder.glob("*.png"))
    if not png_files:
        print(f"在 {image_folder} 中没有找到PNG文件")
        return
    
    print(f"找到 {len(png_files)} 个PNG文件")
    
    # 按文件名排序
    png_files.sort()
    
    extracted_questions = []
    
    for i, image_path in enumerate(png_files, 1):
        print(f"正在处理第 {i}/{len(png_files)} 张图片: {image_path.name}")
        
        # 提取文字
        text = extract_text_from_image(str(image_path), reader)
        
        if text:
            # 解析题目内容
            question, options, correct_answer = parse_question_content(text)
            
            # 格式化题目
            formatted_question = f"题目 {i}:\n"
            if question:
                formatted_question += f"问题: {question}\n"
            
            if options:
                formatted_question += "选项:\n"
                for option in options:
                    formatted_question += f"  {option}\n"
            
            if correct_answer:
                formatted_question += f"正确答案: {correct_answer}\n"
            
            formatted_question += f"原始文本:\n{text}\n"
            formatted_question += "-" * 50 + "\n\n"
            
            extracted_questions.append(formatted_question)
        else:
            print(f"  警告：无法从 {image_path.name} 中提取文字")
    
    # 写入文件
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("考试题目提取结果\n")
            f.write("=" * 50 + "\n\n")
            for question in extracted_questions:
                f.write(question)
        
        print(f"\n成功提取 {len(extracted_questions)} 道题目")
        print(f"结果已保存到: {output_file}")
        
    except Exception as e:
        print(f"保存文件时出错: {e}")

def main():
    # 设置路径
    image_folder = r"C:\Users\<USER>\Desktop\考试题"
    output_file = os.path.join(image_folder, "a.txt")
    
    print("图片题目提取工具")
    print("=" * 30)
    print(f"图片文件夹: {image_folder}")
    print(f"输出文件: {output_file}")
    print()
    
    # 检查文件夹是否存在
    if not os.path.exists(image_folder):
        print(f"错误：文件夹 {image_folder} 不存在")
        print("请确认路径是否正确")
        return
    
    # 开始处理
    process_images(image_folder, output_file)

if __name__ == "__main__":
    main()
