CREATE CATALOG paimon_catalog WITH (
  'type' = 'paimon',
  'warehouse' = 'hdfs:///user/hh_slfn2_sschj/paimon',
  'default-database' = 'ubd_sscj_prod_flink'
);
USE CATALOG paimon_catalog;


SET 'sql-client.execution.result-mode' = 'tableau';
SET 'table.exec.sink.upsert-materialize'='NONE';
SET 'table.exec.sink.not-null-enforcer'='DROP';


--paimon表-外呼数据
CREATE TABLE IF NOT EXISTS `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_tf_f_user_service_temp1` (
  PARTITION_ID STRING,
  USER_ID STRING,
  SERVICE_ID STRING,
  MAIN_TAG STRING,
  START_DATE STRING,
  END_DATE STRING,
  SERVICE_ITEM_ID STRING,
  PACKAGE_ID STRING,
  PRODUCT_ID STRING,
  UPDATE_TIME STRING,
  EPARCHY_CODE STRING,
  PROVINCE_CODE STRING,
  PRIOR_ORDER_TIME STRING,
  opt STRING,
  opttime STRING,
  cdhtime STRING,
  kafka_in_time TIMESTAMP(3),
  kafka_out_time TIMESTAMP(3),
  paimon_time TIMESTAMP(3),
  headers MAP<STRING,BYTES>,
  PRIMARY KEY (USER_ID,SERVICE_ID,START_DATE) NOT ENFORCED
) WITH (
  'bucket' = '256',
  'consumer.expiration-time' = '72h',
  'file.format' = 'avro',
  'log.scan.remove-normalize' = 'true',
  'metadata.stats-mode' = 'none',
  'num-sorted-run.stop-trigger' = '2147483647',
  'sort-spill-threshold' = '10',
  'snapshot.expire.execution-mode' = 'async',
  'snapshot.time-retained' = '6h',
  'tag.automatic-creation' = 'process-time',
  'tag.creation-period' = 'daily',
  'tag.num-retained-max' = '90'
);

