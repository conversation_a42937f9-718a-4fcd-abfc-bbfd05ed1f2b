#!/bin/bash
# 带依赖检查的Flink SQL执行脚本

echo "=== Flink依赖检查和SQL执行脚本 ==="

# 1. 检查Flink环境
if [ -z "$FLINK_HOME" ]; then
    echo "错误: FLINK_HOME环境变量未设置"
    exit 1
fi

echo "Flink Home: $FLINK_HOME"

# 2. 检查必需的JAR包
echo "检查必需的JAR包..."

REQUIRED_JARS=(
    "flink-table-planner"
    "flink-table-api-java-bridge"
    "flink-connector-files"
    "paimon-flink"
)

MISSING_JARS=()

for jar in "${REQUIRED_JARS[@]}"; do
    if ! ls $FLINK_HOME/lib/*${jar}*.jar 1> /dev/null 2>&1; then
        MISSING_JARS+=($jar)
        echo "❌ 缺少: ${jar}*.jar"
    else
        echo "✅ 找到: ${jar}*.jar"
    fi
done

# 3. 如果有缺少的JAR包，提供下载建议
if [ ${#MISSING_JARS[@]} -ne 0 ]; then
    echo ""
    echo "⚠️  缺少以下JAR包，请下载并放入 $FLINK_HOME/lib/ 目录："
    for jar in "${MISSING_JARS[@]}"; do
        case $jar in
            "flink-table-planner")
                echo "  - flink-table-planner_2.12-1.17.x.jar"
                echo "    下载地址: https://repo1.maven.org/maven2/org/apache/flink/flink-table-planner_2.12/"
                ;;
            "flink-table-api-java-bridge")
                echo "  - flink-table-api-java-bridge_2.12-1.17.x.jar"
                echo "    下载地址: https://repo1.maven.org/maven2/org/apache/flink/flink-table-api-java-bridge_2.12/"
                ;;
            "flink-connector-files")
                echo "  - flink-connector-files-1.17.x.jar"
                echo "    下载地址: https://repo1.maven.org/maven2/org/apache/flink/flink-connector-files/"
                ;;
            "paimon-flink")
                echo "  - paimon-flink-1.17-x.x.x.jar"
                echo "    下载地址: https://repo1.maven.org/maven2/org/apache/paimon/paimon-flink-1.17/"
                ;;
        esac
        echo ""
    done
    
    read -p "是否继续执行？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 4. 构建JAR参数
JAR_ARGS=""
for jar_file in $FLINK_HOME/lib/*.jar; do
    if [[ $jar_file == *"flink-table"* ]] || [[ $jar_file == *"flink-connector"* ]] || [[ $jar_file == *"paimon"* ]]; then
        JAR_ARGS="$JAR_ARGS --jar $jar_file"
    fi
done

# 5. 启动Flink SQL Client
echo "启动Flink SQL Client..."
echo "JAR参数: $JAR_ARGS"

# 检查SQL文件是否存在
SQL_FILE="src/load_large_txt_to_paimon_optimized.sql"
if [ ! -f "$SQL_FILE" ]; then
    echo "错误: SQL文件 $SQL_FILE 不存在"
    exit 1
fi

# 启动SQL Client
$FLINK_HOME/bin/sql-client.sh embedded $JAR_ARGS -f $SQL_FILE

echo "执行完成！"
