-- 修复版本：加载大型TXT文件到Paimon表
-- 解决Paimon Catalog下创建filesystem表的问题

-- 0. 添加必需的JAR包（如果在SQL Client中执行）
-- ADD JAR '/path/to/flink-table-planner_2.12-1.17.x.jar';
-- ADD JAR '/path/to/flink-connector-files-1.17.x.jar';
-- ADD JAR '/path/to/paimon-flink-1.17-x.x.x.jar';

-- 1. 设置Paimon Catalog
CREATE CATALOG paimon_catalog WITH (
  'type' = 'paimon',
  'warehouse' = 'hdfs:///user/hh_slfn2_sschj/paimon',
  'default-database' = 'ubd_sscj_prod_flink'
);

-- 2. 设置执行参数
SET 'sql-client.execution.result-mode' = 'tableau';
SET 'table.exec.sink.upsert-materialize' = 'NONE';
SET 'table.exec.sink.not-null-enforcer' = 'DROP';
SET 'table.exec.resource.default-parallelism' = '8';
SET 'pipeline.max-parallelism' = '256';
SET 'execution.checkpointing.interval' = '5min';

-- 3. 创建临时文件源表（必须使用TEMPORARY关键字）
CREATE TEMPORARY TABLE txt_file_source (
  PARTITION_ID STRING,
  USER_ID STRING,
  SERVICE_ID STRING,
  MAIN_TAG STRING,
  START_DATE STRING,
  END_DATE STRING,
  SERVICE_ITEM_ID STRING,
  PACKAGE_ID STRING,
  PRODUCT_ID STRING,
  UPDATE_TIME STRING,
  EPARCHY_CODE STRING,
  PROVINCE_CODE STRING,
  PRIOR_ORDER_TIME STRING,
  opt STRING,
  opttime STRING,
  cdhtime STRING
) WITH (
  'connector' = 'filesystem',
  'path' = 'hdfs://your-hdfs-path/your-file.txt',  -- 替换为实际文件路径
  'format' = 'csv',
  'csv.field-delimiter' = '\u0001',  -- 列分隔符 \001
  'csv.line-delimiter' = '\u0002',   -- 行分隔符 \002
  'csv.ignore-parse-errors' = 'true',
  'csv.allow-comments' = 'false',
  'csv.quote-character' = '',
  'csv.escape-character' = '',
  'csv.null-literal' = ''
);

-- 4. 切换到Paimon Catalog确保目标表存在
USE CATALOG paimon_catalog;

-- 5. 数据写入到Paimon表
INSERT INTO `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_tf_f_user_service_temp1`
SELECT
  PARTITION_ID,
  USER_ID,
  SERVICE_ID,
  MAIN_TAG,
  START_DATE,
  END_DATE,
  SERVICE_ITEM_ID,
  PACKAGE_ID,
  PRODUCT_ID,
  UPDATE_TIME,
  EPARCHY_CODE,
  PROVINCE_CODE,
  PRIOR_ORDER_TIME,
  opt,
  opttime,
  cdhtime,
  NULL AS kafka_in_time,
  NULL AS kafka_out_time,
  CAST(CURRENT_TIMESTAMP AS TIMESTAMP(3)) AS paimon_time,
  CAST(NULL AS MAP<STRING,BYTES>) AS headers
FROM txt_file_source
WHERE USER_ID IS NOT NULL
  AND SERVICE_ID IS NOT NULL
  AND START_DATE IS NOT NULL;
