CREATE CATALOG paimon_catalog WITH (
  'type' = 'paimon',
  'warehouse' = 'hdfs:///user/hh_slfn2_sschj/paimon',
  'default-database' = 'ubd_sscj_prod_flink'
);
USE CATALOG paimon_catalog;
CREATE CATALOG hive_catalog WITH (
  'type' = 'hive',
  'default-database' = 'ubd_sscj_prod_flink',
  'hive-conf-dir' = '/etc/hive/3.1.5.0-152/0'
);
USE CATALOG hive_catalog;
SET
  'sql-client.execution.result-mode' = 'tableau';
SET
  'yarn.application.queue' = 'hh_slfn2_sschj';
SET
  'table.exec.sink.not-null-enforcer' = 'DROP';
SET
  'table.exec.sink.upsert-materialize' = 'NONE';
--kafka表
CREATE TABLE IF NOT EXISTS `hive_catalog`.`ubd_sscj_prod_flink`.`ods_r_kafka_tf_f_user_service` (
  PARTITION_ID STRING,
  USER_ID STRING,
  SERVICE_ID STRING,
  MAIN_TAG STRING,
  START_DATE STRING,
  END_DATE STRING,
  SERVICE_ITEM_ID STRING,
  PACKAGE_ID STRING,
  PRODUCT_ID STRING,
  UPDATE_TIME STRING,
  EPARCHY_CODE STRING,
  PROVINCE_CODE STRING,
  PRIOR_ORDER_TIME STRING,
  opt STRING,
  opttime STRING,
  cdhtime STRING,
  kafkatimestamp TIMESTAMP(3) METADATA FROM 'timestamp',
  currenttime as LOCALTIMESTAMP,
  proctime as PROCTIME(),
  headers MAP<STRING,BYTES> METADATA FROM 'headers'
) WITH (
  'connector' = 'kafka',
  'properties.bootstrap.servers' = '10.177.24.101:9092,10.177.24.102:9092,10.177.24.103:9092,10.177.24.104:9092,10.177.24.105:9092,10.177.24.106:9092,10.177.24.107:9092,10.177.24.108:9092,10.177.24.109:9092,10.177.24.110:9092',
  'topic' = ' CB_TF_F_USER_SERVICE',
  'scan.topic-partition-discovery.interval' = '30000',
  'properties.session.timeout.ms' = '300000',
  'format' = 'csv',
  'csv.ignore-parse-errors' = 'true',
  'csv.field-delimiter' = '\u0001'
);
--paimon表
CREATE TABLE IF NOT EXISTS `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_tf_f_user_service` (
  PARTITION_ID STRING,
  USER_ID STRING,
  SERVICE_ID STRING,
  MAIN_TAG STRING,
  START_DATE STRING,
  END_DATE STRING,
  SERVICE_ITEM_ID STRING,
  PACKAGE_ID STRING,
  PRODUCT_ID STRING,
  UPDATE_TIME STRING,
  EPARCHY_CODE STRING,
  PROVINCE_CODE STRING,
  PRIOR_ORDER_TIME STRING,
  opt STRING,
  opttime STRING,
  cdhtime STRING,
  kafka_in_time TIMESTAMP(3),
  kafka_out_time TIMESTAMP(3),
  paimon_time TIMESTAMP(3),
  headers MAP<STRING,BYTES>,
  PRIMARY KEY (USER_ID,SERVICE_ID,START_DATE) NOT ENFORCED
) WITH (
  'bucket' = '256',
  'consumer.expiration-time' = '72h',
  'file.format' = 'avro',
  'log.scan.remove-normalize' = 'true',
  'metadata.stats-mode' = 'none',
  'num-sorted-run.stop-trigger' = '2147483647',
  'sort-spill-threshold' = '10',
  'snapshot.expire.execution-mode' = 'async',
  'snapshot.time-retained' = '6h',
  'tag.automatic-creation' = 'process-time',
  'tag.creation-period' = 'daily',
  'tag.num-retained-max' = '90'
);
--入湖
insert into
  `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_tf_f_user_service`
select
  PARTITION_ID,
  USER_ID,
  SERVICE_ID,
  MAIN_TAG,
  CASE WHEN CHAR_LENGTH(START_DATE) = 13 THEN FROM_UNIXTIME(CAST(SUBSTRING(START_DATE FROM 0 FOR 10) AS BIGINT)) ELSE START_DATE END,
  CASE WHEN CHAR_LENGTH(END_DATE) = 13 THEN FROM_UNIXTIME(CAST(SUBSTRING(END_DATE FROM 0 FOR 10) AS BIGINT)) ELSE END_DATE END,
  SERVICE_ITEM_ID,
  PACKAGE_ID,
  PRODUCT_ID,
  CASE WHEN CHAR_LENGTH(UPDATE_TIME) = 13 THEN FROM_UNIXTIME(CAST(SUBSTRING(UPDATE_TIME FROM 0 FOR 10) AS BIGINT)) ELSE UPDATE_TIME END,
  EPARCHY_CODE,
  PROVINCE_CODE,
  CASE WHEN CHAR_LENGTH(PRIOR_ORDER_TIME) = 13 THEN FROM_UNIXTIME(CAST(SUBSTRING(PRIOR_ORDER_TIME FROM 0 FOR 10) AS BIGINT)) ELSE PRIOR_ORDER_TIME END,
  opt,
  opttime,
  cdhtime,
  kafkatimestamp kafka_in_time,
  currenttime kafka_out_time,
  LOCALTIMESTAMP AS paimon_time,
  headers
from
  `hive_catalog`.`ubd_sscj_prod_flink`.`ods_r_kafka_tf_f_user_service`/*+ OPTIONS('properties.group.id'='rtag-prod-20231107','scan.startup.mode'='group-offsets','properties.enable.auto.commit'='true','properties.auto.offset.reset.strategy'='latest','properties.auto.commit.interval.ms'='1000')*/
;